<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>柴管家 - 多平台聚合智能客服系统</title>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">



    <style>
        :root {
            --md-sys-color-primary: #1976d2;
            --md-sys-color-on-primary: #ffffff;
            --md-sys-color-primary-container: #e3f2fd;
            --md-sys-color-on-primary-container: #0d47a1;
            --md-sys-color-secondary: #03dac6;
            --md-sys-color-on-secondary: #000000;
            --md-sys-color-surface: #ffffff;
            --md-sys-color-on-surface: #1c1b1f;
            --md-sys-color-surface-variant: #f5f5f5;
            --md-sys-color-on-surface-variant: #49454f;
            --md-sys-color-error: #ba1a1a;
            --md-sys-color-on-error: #ffffff;
            --md-sys-color-outline: #79747e;
            --md-sys-color-outline-variant: #cac4d0;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background-color: var(--md-sys-color-surface, #ffffff);
            color: var(--md-sys-color-on-surface, #1c1b1f);
            line-height: 1.5;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .app-container {
            display: flex;
            min-height: 100vh;
            width: 100%;
        }

        /* 页面容器 */
        .page {
            display: none;
            width: 100%;
            min-height: 100vh;
            position: relative;
        }

        .page.active {
            display: flex;
            flex-direction: column;
        }

        /* 登录页面样式 */
        .login-page {
            justify-content: center;
            align-items: center;
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        }

        .login-card {
            background: var(--md-sys-color-surface);
            border-radius: 28px;
            padding: 48px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 400px;
            text-align: center;
        }

        .logo {
            width: 80px;
            height: 80px;
            background: var(--md-sys-color-primary);
            border-radius: 20px;
            margin: 0 auto 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--md-sys-color-on-primary);
            font-size: 32px;
            font-weight: 700;
        }

        .login-form {
            display: flex;
            flex-direction: column;
            gap: 24px;
            margin-top: 32px;
        }

        .form-field {
            position: relative;
        }

        .form-field md-filled-text-field {
            width: 100%;
        }

        .login-tabs {
            display: flex;
            background: var(--md-sys-color-surface-variant);
            border-radius: 12px;
            padding: 4px;
            margin-bottom: 24px;
        }

        .login-tab {
            flex: 1;
            padding: 12px;
            border: none;
            background: transparent;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.2s;
        }

        .login-tab.active {
            background: var(--md-sys-color-primary);
            color: var(--md-sys-color-on-primary);
        }

        .verification-row {
            display: flex;
            gap: 12px;
            align-items: flex-end;
        }

        .verification-row .input-container {
            flex: 1;
        }

        .verification-row .material-button {
            height: 56px;
            border-radius: 4px;
        }

        .countdown-btn {
            min-width: 120px;
        }

        /* Material Design 输入框样式 */
        .input-container {
            position: relative;
            margin-bottom: 8px;
        }

        .material-input {
            width: 100%;
            height: 56px;
            padding: 16px 16px 8px 16px;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            font-size: 16px;
            font-family: 'Roboto', sans-serif;
            background-color: #f5f5f5;
            transition: all 0.2s ease;
            outline: none;
            box-sizing: border-box;
        }

        .material-input:focus {
            border-color: #1976d2;
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(25, 118, 210, 0.2);
        }

        .input-label {
            position: absolute;
            left: 16px;
            top: 8px;
            font-size: 12px;
            color: #666;
            font-family: 'Roboto', sans-serif;
            pointer-events: none;
            transition: all 0.2s ease;
        }

        .material-input:focus + .input-label {
            color: #1976d2;
        }

        /* Material Design 按钮样式 */
        .material-button {
            padding: 0 24px;
            height: 40px;
            border: none;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            font-family: 'Roboto', sans-serif;
            cursor: pointer;
            transition: all 0.2s ease;
            outline: none;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .material-button.primary {
            background-color: #1976d2;
            color: white;
        }

        .material-button.primary:hover {
            background-color: #1565c0;
            box-shadow: 0 2px 8px rgba(25, 118, 210, 0.3);
        }

        .material-button.primary:active {
            background-color: #0d47a1;
        }

        .material-button.outlined {
            background-color: transparent;
            color: #1976d2;
            border: 1px solid #1976d2;
        }

        .material-button.outlined:hover {
            background-color: rgba(25, 118, 210, 0.08);
            box-shadow: 0 2px 8px rgba(25, 118, 210, 0.2);
        }

        .material-button.outlined:active {
            background-color: rgba(25, 118, 210, 0.16);
        }

        /* 聊天输入框样式 */
        .chat-input-container {
            flex: 1;
            position: relative;
        }

        .chat-input-field {
            width: 100%;
            height: 40px;
            padding: 8px 16px;
            border: 1px solid #e0e0e0;
            border-radius: 20px;
            font-size: 14px;
            font-family: 'Roboto', sans-serif;
            background-color: #f5f5f5;
            transition: all 0.2s ease;
            outline: none;
            box-sizing: border-box;
        }

        .chat-input-field:focus {
            border-color: #1976d2;
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(25, 118, 210, 0.2);
        }

        /* 图标按钮样式 */
        .icon-button {
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 50%;
            background-color: transparent;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            outline: none;
        }

        .icon-button:hover {
            background-color: rgba(25, 118, 210, 0.08);
        }

        .icon-button .material-icons {
            font-size: 20px;
            color: #666;
        }

        /* FAB按钮样式 */
        .fab-button {
            width: 56px;
            height: 56px;
            border: none;
            border-radius: 50%;
            background-color: #1976d2;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            outline: none;
            box-shadow: 0 3px 5px -1px rgba(0,0,0,.2), 0 6px 10px 0 rgba(0,0,0,.14), 0 1px 18px 0 rgba(0,0,0,.12);
        }

        .fab-button:hover {
            background-color: #1565c0;
            box-shadow: 0 5px 5px -3px rgba(0,0,0,.2), 0 8px 10px 1px rgba(0,0,0,.14), 0 3px 14px 2px rgba(0,0,0,.12);
        }

        .fab-button .material-icons {
            font-size: 24px;
        }

        /* 搜索输入框样式 */
        .search-input-container {
            position: relative;
            width: 100%;
            display: flex;
            align-items: center;
        }

        .search-input {
            width: 100%;
            height: 40px;
            padding: 8px 16px 8px 48px;
            border: 1px solid #e0e0e0;
            border-radius: 20px;
            font-size: 14px;
            font-family: 'Roboto', sans-serif;
            background-color: #f5f5f5;
            transition: all 0.2s ease;
            outline: none;
        }

        .search-input:focus {
            border-color: #1976d2;
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(25, 118, 210, 0.2);
        }

        .search-icon {
            position: absolute;
            left: 16px;
            color: #666;
            font-size: 20px;
            z-index: 1;
        }

        /* 图标按钮样式 */
        .icon-button {
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 20px;
            background: transparent;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            color: #666;
        }

        .icon-button:hover {
            background-color: rgba(25, 118, 210, 0.08);
            color: #1976d2;
        }

        .icon-button .material-icons {
            font-size: 20px;
        }

        /* 过滤芯片样式 */
        .filter-chip {
            padding: 6px 12px;
            border: 1px solid #e0e0e0;
            border-radius: 16px;
            background: #fff;
            color: #666;
            font-size: 12px;
            font-family: 'Roboto', sans-serif;
            cursor: pointer;
            transition: all 0.2s ease;
            outline: none;
        }

        .filter-chip:hover {
            background: #f5f5f5;
            border-color: #1976d2;
        }

        .filter-chip.active {
            background: #1976d2;
            color: #fff;
            border-color: #1976d2;
        }

        /* 开关样式 */
        .switch {
            position: relative;
            display: inline-block;
            width: 44px;
            height: 24px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: #1976d2;
        }

        input:checked + .slider:before {
            transform: translateX(20px);
        }

        /* 文本按钮样式 */
        .text-button {
            background: none;
            border: none;
            color: #1976d2;
            font-size: 14px;
            font-family: 'Roboto', sans-serif;
            cursor: pointer;
            padding: 8px 12px;
            border-radius: 4px;
            transition: all 0.2s ease;
            outline: none;
        }

        .text-button:hover {
            background-color: rgba(25, 118, 210, 0.08);
        }

        /* 主应用布局 */
        .main-app {
            flex-direction: row;
            width: 100%;
            height: 100vh;
            overflow: hidden;
        }

        .main-app.active {
            display: flex;
            flex-direction: row;
        }

        .sidebar {
            width: 280px;
            min-width: 280px;
            background: var(--md-sys-color-surface-variant, #f5f5f5);
            border-right: 1px solid var(--md-sys-color-outline-variant, #cac4d0);
            display: flex;
            flex-direction: column;
            height: 100vh;
            overflow-y: auto;
        }

        .sidebar-header {
            padding: 24px;
            border-bottom: 1px solid var(--md-sys-color-outline-variant);
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .sidebar-logo {
            width: 40px;
            height: 40px;
            background: var(--md-sys-color-primary);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--md-sys-color-on-primary);
            font-weight: 700;
        }

        .nav-menu {
            flex: 1;
            padding: 16px 0;
        }

        .nav-item {
            display: flex;
            align-items: center;
            gap: 16px;
            padding: 16px 24px;
            cursor: pointer;
            transition: all 0.2s;
            border: none;
            background: none;
            width: 100%;
            text-align: left;
            color: var(--md-sys-color-on-surface-variant);
        }

        .nav-item:hover {
            background: rgba(25, 118, 210, 0.08);
        }

        .nav-item.active {
            background: var(--md-sys-color-primary-container);
            color: var(--md-sys-color-on-primary-container);
            border-right: 3px solid var(--md-sys-color-primary);
        }

        .nav-item .material-icons {
            font-size: 24px;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            height: 100vh;
            overflow: hidden;
            min-width: 0;
        }

        .top-bar {
            height: 64px;
            min-height: 64px;
            background: var(--md-sys-color-surface, #ffffff);
            border-bottom: 1px solid var(--md-sys-color-outline-variant, #cac4d0);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 24px;
            flex-shrink: 0;
        }

        .search-bar {
            flex: 1;
            max-width: 400px;
            margin: 0 24px;
        }

        .user-menu {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .content-area {
            flex: 1;
            overflow: hidden;
            position: relative;
            min-height: 0;
        }

        .content-view {
            display: none;
            height: 100%;
            width: 100%;
            overflow: hidden;
        }

        .content-view.active {
            display: flex;
            flex-direction: column;
        }

        /* 工作台样式 */
        .workspace {
            display: flex;
            height: 100%;
            width: 100%;
            overflow: hidden;
        }

        .conversation-list {
            width: 350px;
            min-width: 350px;
            border-right: 1px solid var(--md-sys-color-outline-variant, #cac4d0);
            display: flex;
            flex-direction: column;
            height: 100%;
            overflow: hidden;
        }

        .conversation-header {
            padding: 16px;
            border-bottom: 1px solid var(--md-sys-color-outline-variant);
        }

        .conversation-scroll {
            flex: 1;
            overflow-y: auto;
            overflow-x: hidden;
        }

        .conversation-item {
            padding: 16px;
            border-bottom: 1px solid var(--md-sys-color-outline-variant);
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            gap: 12px;
        }

        .conversation-item:hover {
            background: var(--md-sys-color-surface-variant);
        }

        .conversation-item.active {
            background: var(--md-sys-color-primary-container);
        }

        .conversation-avatar {
            width: 48px;
            height: 48px;
            border-radius: 24px;
            background: var(--md-sys-color-secondary);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--md-sys-color-on-secondary);
            font-weight: 500;
            flex-shrink: 0;
        }

        .conversation-info {
            flex: 1;
            min-width: 0;
        }

        .conversation-name {
            font-weight: 500;
            margin-bottom: 4px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .platform-badge {
            background: var(--md-sys-color-primary);
            color: var(--md-sys-color-on-primary);
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .conversation-preview {
            color: var(--md-sys-color-on-surface-variant);
            font-size: 14px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .conversation-meta {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-top: 4px;
        }

        .conversation-time {
            font-size: 12px;
            color: var(--md-sys-color-on-surface-variant);
        }

        .unread-badge {
            background: var(--md-sys-color-error);
            color: var(--md-sys-color-on-error);
            border-radius: 12px;
            padding: 2px 8px;
            font-size: 12px;
            font-weight: 500;
            min-width: 20px;
            text-align: center;
        }

        .chat-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            height: 100%;
            min-width: 0;
            overflow: hidden;
        }

        .chat-header {
            padding: 16px 24px;
            border-bottom: 1px solid var(--md-sys-color-outline-variant);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .chat-title {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .ai-toggle {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .ai-status {
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 500;
        }

        .ai-status.manual {
            background: var(--md-sys-color-surface-variant);
            color: var(--md-sys-color-on-surface-variant);
        }

        .ai-status.auto {
            background: var(--md-sys-color-primary);
            color: var(--md-sys-color-on-primary);
        }

        .ai-status.pending {
            background: #ff9800;
            color: white;
        }

        .chat-messages {
            flex: 1;
            padding: 16px;
            overflow-y: auto;
            overflow-x: hidden;
            display: flex;
            flex-direction: column;
            gap: 16px;
            min-height: 0;
            background: var(--md-sys-color-surface, #ffffff);
        }

        .message {
            display: flex;
            gap: 12px;
            max-width: 70%;
        }

        .message.sent {
            align-self: flex-end;
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 32px;
            height: 32px;
            border-radius: 16px;
            background: var(--md-sys-color-secondary);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--md-sys-color-on-secondary);
            font-size: 14px;
            flex-shrink: 0;
        }

        .message-content {
            background: var(--md-sys-color-surface-variant);
            padding: 12px 16px;
            border-radius: 16px;
            position: relative;
        }

        .message.sent .message-content {
            background: var(--md-sys-color-primary);
            color: var(--md-sys-color-on-primary);
        }

        .message-time {
            font-size: 12px;
            color: var(--md-sys-color-on-surface-variant);
            margin-top: 4px;
        }

        .ai-assistant {
            width: 300px;
            min-width: 300px;
            border-left: 1px solid var(--md-sys-color-outline-variant, #cac4d0);
            background: var(--md-sys-color-surface-variant, #f5f5f5);
            padding: 16px;
            height: 100%;
            overflow-y: auto;
            overflow-x: hidden;
        }

        .ai-analysis {
            background: var(--md-sys-color-surface);
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
        }

        .ai-suggestions {
            background: var(--md-sys-color-surface);
            border-radius: 12px;
            padding: 16px;
        }

        .suggestion-item {
            background: var(--md-sys-color-primary-container);
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .suggestion-item:hover {
            background: var(--md-sys-color-primary);
            color: var(--md-sys-color-on-primary);
        }

        .chat-input {
            padding: 16px 24px;
            border-top: 1px solid var(--md-sys-color-outline-variant, #cac4d0);
            display: flex;
            gap: 12px;
            align-items: flex-end;
            background: var(--md-sys-color-surface, #ffffff);
            flex-shrink: 0;
            min-height: 80px;
        }

        .input-field {
            flex: 1;
        }

        /* 渠道管理样式 */
        .channel-grid {
            padding: 24px;
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 24px;
        }

        .channel-card {
            background: var(--md-sys-color-surface);
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border: 1px solid var(--md-sys-color-outline-variant);
        }

        .channel-header {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 16px;
        }

        .channel-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            background: var(--md-sys-color-primary);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--md-sys-color-on-primary);
            font-size: 24px;
        }

        .channel-info h3 {
            margin-bottom: 4px;
        }

        .channel-status {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }

        .status-dot.online {
            background: #4caf50;
        }

        .status-dot.offline {
            background: #f44336;
        }

        .channel-stats {
            margin: 16px 0;
            padding: 16px;
            background: var(--md-sys-color-surface-variant);
            border-radius: 8px;
        }

        .stat-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }

        .channel-actions {
            display: flex;
            gap: 8px;
            justify-content: flex-end;
        }

        /* 添加渠道FAB */
        .add-channel-fab {
            position: fixed;
            bottom: 24px;
            right: 24px;
            z-index: 1000;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .app-container {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                height: auto;
                order: 2;
            }

            .main-content {
                order: 1;
            }

            .workspace {
                flex-direction: column;
            }

            .conversation-list {
                width: 100%;
                height: 300px;
            }

            .ai-assistant {
                width: 100%;
                height: 200px;
            }

            .channel-grid {
                grid-template-columns: 1fr;
            }
        }

        /* 动画效果 */
        .fade-in {
            animation: fadeIn 0.3s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .slide-in {
            animation: slideIn 0.3s ease-in-out;
        }

        @keyframes slideIn {
            from { transform: translateX(-100%); }
            to { transform: translateX(0); }
        }

        /* 隐藏类 */
        .hidden {
            display: none !important;
        }

        /* 知识库样式 */
        .knowledge-container {
            padding: 24px;
            height: 100%;
            overflow-y: auto;
        }

        .knowledge-tabs {
            display: flex;
            gap: 8px;
            margin-bottom: 24px;
            border-bottom: 1px solid var(--md-sys-color-outline-variant);
        }

        .knowledge-tab {
            padding: 12px 24px;
            border: none;
            background: none;
            color: var(--md-sys-color-on-surface-variant);
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.2s;
        }

        .knowledge-tab.active {
            color: var(--md-sys-color-primary);
            border-bottom-color: var(--md-sys-color-primary);
        }

        .knowledge-section {
            display: none;
        }

        .knowledge-section.active {
            display: block;
        }

        .faq-list {
            display: grid;
            gap: 16px;
        }

        .faq-item {
            background: var(--md-sys-color-surface);
            border: 1px solid var(--md-sys-color-outline-variant);
            border-radius: 12px;
            padding: 20px;
            transition: all 0.2s;
        }

        .faq-item:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .faq-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .faq-question {
            font-weight: 500;
            color: var(--md-sys-color-on-surface);
        }

        .faq-status {
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 500;
        }

        .faq-status.active {
            background: var(--md-sys-color-primary-container);
            color: var(--md-sys-color-on-primary-container);
        }

        .faq-status.draft {
            background: var(--md-sys-color-surface-variant);
            color: var(--md-sys-color-on-surface-variant);
        }

        .faq-answer {
            color: var(--md-sys-color-on-surface-variant);
            margin-bottom: 16px;
            line-height: 1.5;
        }

        .faq-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
            color: var(--md-sys-color-on-surface-variant);
        }

        .faq-actions {
            display: flex;
            gap: 8px;
        }

        /* 数据分析样式 */
        .analytics-container {
            padding: 24px;
            height: 100%;
            overflow-y: auto;
        }

        .analytics-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }

        .metric-card {
            background: var(--md-sys-color-surface);
            border: 1px solid var(--md-sys-color-outline-variant);
            border-radius: 16px;
            padding: 24px;
            text-align: center;
        }

        .metric-value {
            font-size: 32px;
            font-weight: 700;
            color: var(--md-sys-color-primary);
            margin-bottom: 8px;
        }

        .metric-label {
            color: var(--md-sys-color-on-surface-variant);
            font-size: 14px;
        }

        .metric-change {
            margin-top: 8px;
            font-size: 12px;
            font-weight: 500;
        }

        .metric-change.positive {
            color: #4caf50;
        }

        .metric-change.negative {
            color: #f44336;
        }

        .chart-container {
            background: var(--md-sys-color-surface);
            border: 1px solid var(--md-sys-color-outline-variant);
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 24px;
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .chart-title {
            font-size: 18px;
            font-weight: 500;
        }

        .chart-placeholder {
            height: 300px;
            background: var(--md-sys-color-surface-variant);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--md-sys-color-on-surface-variant);
        }

        /* 系统设置样式 */
        .settings-container {
            padding: 24px;
            height: 100%;
            overflow-y: auto;
        }

        .settings-nav {
            display: flex;
            gap: 24px;
            margin-bottom: 32px;
            border-bottom: 1px solid var(--md-sys-color-outline-variant);
        }

        .settings-nav-item {
            padding: 12px 0;
            border: none;
            background: none;
            color: var(--md-sys-color-on-surface-variant);
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.2s;
            font-size: 16px;
        }

        .settings-nav-item.active {
            color: var(--md-sys-color-primary);
            border-bottom-color: var(--md-sys-color-primary);
        }

        .settings-section {
            display: none;
        }

        .settings-section.active {
            display: block;
        }

        .settings-group {
            background: var(--md-sys-color-surface);
            border: 1px solid var(--md-sys-color-outline-variant);
            border-radius: 12px;
            margin-bottom: 24px;
            overflow: hidden;
        }

        .settings-group-header {
            padding: 20px 24px;
            border-bottom: 1px solid var(--md-sys-color-outline-variant);
            background: var(--md-sys-color-surface-variant);
        }

        .settings-group-title {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 4px;
        }

        .settings-group-description {
            font-size: 14px;
            color: var(--md-sys-color-on-surface-variant);
        }

        .settings-item {
            padding: 20px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid var(--md-sys-color-outline-variant);
        }

        .settings-item:last-child {
            border-bottom: none;
        }

        .settings-item-info {
            flex: 1;
        }

        .settings-item-title {
            font-weight: 500;
            margin-bottom: 4px;
        }

        .settings-item-description {
            font-size: 14px;
            color: var(--md-sys-color-on-surface-variant);
        }

        .settings-control {
            flex-shrink: 0;
            margin-left: 16px;
        }

        /* 表单样式增强 */
        .form-row {
            display: flex;
            gap: 16px;
            margin-bottom: 20px;
        }

        .form-group {
            flex: 1;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--md-sys-color-on-surface);
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid var(--md-sys-color-outline);
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.2s;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: var(--md-sys-color-primary);
            box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
        }

        .form-group textarea {
            min-height: 100px;
            resize: vertical;
        }

        /* 错误提示样式 */
        .error-message {
            color: var(--md-sys-color-error);
            font-size: 14px;
            margin-top: 8px;
        }

        /* 成功提示样式 */
        .success-message {
            color: #4caf50;
            font-size: 14px;
            margin-top: 8px;
        }
    </style>
</head>
<body>
    <!-- 登录页面 -->
    <div id="loginPage" class="page login-page active">
        <div class="login-card fade-in">
            <!-- 调试模式标识 -->
            <div style="background: #ff9800; color: white; padding: 8px 16px; border-radius: 20px; font-size: 12px; margin-bottom: 16px; text-align: center; font-weight: 500;">
                🚀 调试模式 - 点击登录即可进入
            </div>

            <div class="logo">柴</div>
            <h1 class="md-typescale-headline-medium">柴管家</h1>
            <p class="md-typescale-body-medium" style="color: var(--md-sys-color-on-surface-variant); margin-top: 8px;">
                多平台聚合智能客服系统
            </p>

            <div class="login-form">
                <div class="login-tabs">
                    <button class="login-tab active" onclick="switchLoginTab('sms')">验证码登录</button>
                    <button class="login-tab" onclick="switchLoginTab('password')">密码登录</button>
                </div>

                <div class="form-field">
                    <div class="input-container">
                        <input
                            id="phoneInput"
                            type="tel"
                            maxlength="11"
                            placeholder="请输入手机号"
                            class="material-input">
                        <label for="phoneInput" class="input-label">手机号</label>
                    </div>
                    <div id="phoneError" class="error-message hidden"></div>
                </div>

                <!-- 验证码登录表单 -->
                <div id="smsForm">
                    <div class="form-field">
                        <div class="verification-row">
                            <div class="input-container">
                                <input
                                    id="smsCodeInput"
                                    type="text"
                                    maxlength="6"
                                    placeholder="请输入验证码"
                                    class="material-input">
                                <label for="smsCodeInput" class="input-label">验证码</label>
                            </div>
                            <button
                                id="getSmsBtn"
                                class="material-button primary countdown-btn"
                                onclick="getSmsCode()">
                                获取验证码
                            </button>
                        </div>
                        <div id="smsError" class="error-message hidden"></div>
                    </div>
                </div>

                <!-- 密码登录表单 -->
                <div id="passwordForm" class="hidden">
                    <div class="form-field">
                        <div class="input-container">
                            <input
                                id="passwordInput"
                                type="password"
                                placeholder="请输入密码"
                                class="material-input">
                            <label for="passwordInput" class="input-label">密码</label>
                        </div>
                        <div id="passwordError" class="error-message hidden"></div>
                    </div>
                </div>

                <button onclick="login()" class="material-button primary" style="width: 100%; height: 48px;">
                    登录
                </button>

                <div style="text-align: center; margin-top: 16px;">
                    <button class="text-button" onclick="showRegister()">立即注册</button>
                    <span style="margin: 0 8px; color: var(--md-sys-color-outline);">|</span>
                    <button class="text-button" onclick="showForgotPassword()">忘记密码</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 主应用页面 -->
    <div id="mainApp" class="page main-app">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">柴</div>
                <div>
                    <h3 class="md-typescale-title-medium">柴管家</h3>
                    <p class="md-typescale-body-small" style="color: var(--md-sys-color-on-surface-variant);">
                        智能客服系统
                    </p>
                </div>
            </div>

            <nav class="nav-menu">
                <button class="nav-item active" onclick="switchView('workspace')">
                    <span class="material-icons">dashboard</span>
                    <span>工作台</span>
                </button>
                <button class="nav-item" onclick="switchView('channels')">
                    <span class="material-icons">hub</span>
                    <span>渠道管理</span>
                </button>
                <button class="nav-item" onclick="switchView('knowledge')">
                    <span class="material-icons">psychology</span>
                    <span>知识库</span>
                </button>
                <button class="nav-item" onclick="switchView('analytics')">
                    <span class="material-icons">analytics</span>
                    <span>数据分析</span>
                </button>
                <button class="nav-item" onclick="switchView('settings')">
                    <span class="material-icons">settings</span>
                    <span>系统设置</span>
                </button>
            </nav>

            <div style="padding: 24px; border-top: 1px solid var(--md-sys-color-outline-variant);">
                <div style="display: flex; align-items: center; gap: 12px;">
                    <div style="width: 32px; height: 32px; border-radius: 16px; background: var(--md-sys-color-primary); display: flex; align-items: center; justify-content: center; color: var(--md-sys-color-on-primary); font-weight: 500;">
                        思
                    </div>
                    <div>
                        <div class="md-typescale-body-medium">思思</div>
                        <div class="md-typescale-body-small" style="color: var(--md-sys-color-on-surface-variant);">
                            知识IP主理人
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 顶部栏 -->
            <div class="top-bar">
                <h2 id="pageTitle" class="md-typescale-headline-small">工作台</h2>

                <div class="search-bar">
                    <div class="search-input-container">
                        <span class="material-icons search-icon">search</span>
                        <input
                            type="search"
                            placeholder="搜索消息和联系人"
                            class="search-input">
                    </div>
                </div>

                <div class="user-menu">
                    <button class="icon-button" onclick="showNotifications()">
                        <span class="material-icons">notifications</span>
                    </button>
                    <button class="icon-button" onclick="showUserMenu()">
                        <span class="material-icons">account_circle</span>
                    </button>
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="content-area">
                <!-- 工作台视图 -->
                <div id="workspaceView" class="content-view active">
                    <div class="workspace">
                        <!-- 会话列表 -->
                        <div class="conversation-list">
                            <div class="conversation-header">
                                <h3 class="md-typescale-title-medium">消息中心</h3>
                                <div style="display: flex; gap: 8px; margin-top: 8px;">
                                    <button class="filter-chip active">全部</button>
                                    <button class="filter-chip">未读</button>
                                    <button class="filter-chip">微信</button>
                                    <button class="filter-chip">抖音</button>
                                </div>
                            </div>

                            <div class="conversation-scroll">
                                <div class="conversation-item active" onclick="selectConversation(this, '张三')">
                                    <div class="conversation-avatar">张</div>
                                    <div class="conversation-info">
                                        <div class="conversation-name">
                                            张三
                                            <span class="platform-badge">微信</span>
                                        </div>
                                        <div class="conversation-preview">你好，请问有什么产品？</div>
                                        <div class="conversation-meta">
                                            <span class="conversation-time">15:30</span>
                                            <span class="unread-badge">2</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="conversation-item" onclick="selectConversation(this, '李四')">
                                    <div class="conversation-avatar">李</div>
                                    <div class="conversation-info">
                                        <div class="conversation-name">
                                            李四
                                            <span class="platform-badge" style="background: #ff6b35;">抖音</span>
                                        </div>
                                        <div class="conversation-preview">价格怎么样？</div>
                                        <div class="conversation-meta">
                                            <span class="conversation-time">14:20</span>
                                            <span class="unread-badge">1</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="conversation-item" onclick="selectConversation(this, '王五')">
                                    <div class="conversation-avatar">王</div>
                                    <div class="conversation-info">
                                        <div class="conversation-name">
                                            王五
                                            <span class="platform-badge" style="background: #1890ff;">钉钉</span>
                                        </div>
                                        <div class="conversation-preview">什么时候发货？</div>
                                        <div class="conversation-meta">
                                            <span class="conversation-time">13:10</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 聊天区域 -->
                        <div class="chat-area">
                            <div class="chat-header">
                                <div class="chat-title">
                                    <div class="conversation-avatar" style="width: 40px; height: 40px;">张</div>
                                    <div>
                                        <h3 class="md-typescale-title-medium">张三</h3>
                                        <p class="md-typescale-body-small" style="color: var(--md-sys-color-on-surface-variant);">
                                            微信 - 主店铺
                                        </p>
                                    </div>
                                </div>

                                <div class="ai-toggle">
                                    <span class="ai-status manual" id="aiStatus">人工模式</span>
                                    <label class="switch">
                                        <input type="checkbox" id="aiSwitch" onclick="toggleAIMode()">
                                        <span class="slider"></span>
                                    </label>
                                </div>
                            </div>

                            <div class="chat-messages" id="chatMessages">
                                <div class="message">
                                    <div class="message-avatar">张</div>
                                    <div class="message-content">
                                        <div>你好，请问有什么产品？</div>
                                        <div class="message-time">15:28</div>
                                    </div>
                                </div>

                                <div class="message sent">
                                    <div class="message-avatar">思</div>
                                    <div class="message-content">
                                        <div>您好！感谢您的咨询。我们主要有A产品和B产品两个系列，都是针对不同需求设计的。请问您比较关注哪个方面呢？</div>
                                        <div class="message-time">15:30</div>
                                    </div>
                                </div>

                                <div class="message">
                                    <div class="message-avatar">张</div>
                                    <div class="message-content">
                                        <div>价格大概是多少？</div>
                                        <div class="message-time">15:32</div>
                                    </div>
                                </div>
                            </div>

                            <div class="chat-input">
                                <div class="chat-input-container">
                                    <input
                                        id="messageInput"
                                        class="chat-input-field"
                                        type="text"
                                        placeholder="输入消息..."
                                        onkeypress="handleEnterKey(event)">
                                </div>
                                <button class="icon-button" onclick="attachFile()">
                                    <span class="material-icons">attach_file</span>
                                </button>
                                <button class="material-button primary" onclick="sendMessage()">
                                    <span class="material-icons">send</span>
                                </button>
                            </div>
                        </div>

                        <!-- AI助手面板 -->
                        <div class="ai-assistant">
                            <div class="ai-analysis">
                                <h4 class="md-typescale-title-small" style="margin-bottom: 12px;">
                                    <span class="material-icons" style="vertical-align: middle; margin-right: 8px;">psychology</span>
                                    AI分析
                                </h4>
                                <div style="background: var(--md-sys-color-primary-container); padding: 12px; border-radius: 8px; margin-bottom: 8px;">
                                    <strong>意图识别：</strong>咨询产品价格
                                </div>
                                <div style="background: var(--md-sys-color-secondary-container); padding: 12px; border-radius: 8px;">
                                    <strong>置信度：</strong>85%
                                </div>
                            </div>

                            <div class="ai-suggestions">
                                <h4 class="md-typescale-title-small" style="margin-bottom: 12px;">
                                    <span class="material-icons" style="vertical-align: middle; margin-right: 8px;">lightbulb</span>
                                    回复建议
                                </h4>

                                <div class="suggestion-item" onclick="useSuggestion(this)">
                                    我们的A产品价格在299-599元之间，B产品价格在199-399元之间。具体价格会根据您选择的配置有所不同，我可以为您详细介绍一下吗？
                                </div>

                                <div class="suggestion-item" onclick="useSuggestion(this)">
                                    感谢您对价格的关注！我们有不同价位的产品可以满足您的需求。请问您的预算大概在什么范围呢？这样我可以为您推荐最合适的产品。
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 渠道管理视图 -->
                <div id="channelsView" class="content-view">
                    <div class="channel-grid">
                        <div class="channel-card fade-in">
                            <div class="channel-header">
                                <div class="channel-icon">📱</div>
                                <div class="channel-info">
                                    <h3 class="md-typescale-title-medium">微信 - 主店铺</h3>
                                    <div class="channel-status">
                                        <div class="status-dot online"></div>
                                        <span>在线</span>
                                    </div>
                                </div>
                            </div>

                            <div class="channel-stats">
                                <div class="stat-row">
                                    <span>今日消息</span>
                                    <strong>25条</strong>
                                </div>
                                <div class="stat-row">
                                    <span>最后活跃</span>
                                    <span>15:30</span>
                                </div>
                                <div class="stat-row">
                                    <span>连接时间</span>
                                    <span>2025-01-27 10:30</span>
                                </div>
                            </div>

                            <div class="channel-actions">
                                <button class="material-button outlined" onclick="editChannel('wechat1')">编辑</button>
                                <button class="material-button outlined" onclick="pauseChannel('wechat1')">暂停</button>
                            </div>
                        </div>

                        <div class="channel-card fade-in">
                            <div class="channel-header">
                                <div class="channel-icon" style="background: #ff6b35;">📺</div>
                                <div class="channel-info">
                                    <h3 class="md-typescale-title-medium">抖音 - 官方号</h3>
                                    <div class="channel-status">
                                        <div class="status-dot online"></div>
                                        <span>在线</span>
                                    </div>
                                </div>
                            </div>

                            <div class="channel-stats">
                                <div class="stat-row">
                                    <span>今日消息</span>
                                    <strong>18条</strong>
                                </div>
                                <div class="stat-row">
                                    <span>最后活跃</span>
                                    <span>14:20</span>
                                </div>
                                <div class="stat-row">
                                    <span>连接时间</span>
                                    <span>2025-01-27 09:15</span>
                                </div>
                            </div>

                            <div class="channel-actions">
                                <button class="material-button outlined" onclick="editChannel('douyin1')">编辑</button>
                                <button class="material-button outlined" onclick="pauseChannel('douyin1')">暂停</button>
                            </div>
                        </div>

                        <div class="channel-card fade-in">
                            <div class="channel-header">
                                <div class="channel-icon" style="background: #1890ff;">💼</div>
                                <div class="channel-info">
                                    <h3 class="md-typescale-title-medium">钉钉 - 客服群</h3>
                                    <div class="channel-status">
                                        <div class="status-dot online"></div>
                                        <span>在线</span>
                                    </div>
                                </div>
                            </div>

                            <div class="channel-stats">
                                <div class="stat-row">
                                    <span>今日消息</span>
                                    <strong>12条</strong>
                                </div>
                                <div class="stat-row">
                                    <span>最后活跃</span>
                                    <span>13:10</span>
                                </div>
                                <div class="stat-row">
                                    <span>连接时间</span>
                                    <span>2025-01-27 08:45</span>
                                </div>
                            </div>

                            <div class="channel-actions">
                                <button class="material-button outlined" onclick="editChannel('dingtalk1')">编辑</button>
                                <button class="material-button outlined" onclick="pauseChannel('dingtalk1')">暂停</button>
                            </div>
                        </div>
                    </div>

                    <!-- 添加渠道FAB -->
                    <button class="fab-button add-channel-fab" onclick="addChannel()">
                        <span class="material-icons">add</span>
                    </button>
                </div>

                <!-- 知识库视图 -->
                <div id="knowledgeView" class="content-view">
                    <div class="knowledge-container">
                        <h2 class="md-typescale-headline-medium">知识库管理</h2>
                        <p class="md-typescale-body-medium" style="color: var(--md-sys-color-on-surface-variant); margin-top: 8px;">
                            管理AI回复的知识库内容，提升回复质量和准确性
                        </p>

                        <!-- 顶部工具条 -->
                        <div class="form-row" style="margin-top: 16px; align-items: flex-end;">
                            <div class="form-group">
                                <label>搜索知识</label>
                                <input id="kbSearchInput" type="text" placeholder="输入关键词快速搜索（问题、标签、来源）" oninput="filterKnowledge()" />
                            </div>
                            <div class="form-group" style="max-width: 220px;">
                                <label>来源</label>
                                <select id="kbSourceFilter" onchange="filterKnowledge()">
                                    <option value="all">全部来源</option>
                                    <option value="faq">FAQ</option>
                                    <option value="doc">文档</option>
                                    <option value="chat">对话挖掘</option>
                                </select>
                            </div>
                            <div class="form-group" style="max-width: 220px;">
                                <label>状态</label>
                                <select id="kbStatusFilter" onchange="filterKnowledge()">
                                    <option value="all">全部状态</option>
                                    <option value="active">启用</option>
                                    <option value="draft">草稿</option>
                                </select>
                            </div>
                            <button class="material-button filled" onclick="openKnowledgeEditor()">新建知识</button>
                        </div>

                        <!-- Tab -->
                        <div class="knowledge-tabs">
                            <button class="knowledge-tab active" onclick="switchKnowledgeTab(event, 'faq')">FAQ</button>
                            <button class="knowledge-tab" onclick="switchKnowledgeTab(event, 'docs')">文档</button>
                            <button class="knowledge-tab" onclick="switchKnowledgeTab(event, 'mined')">对话挖掘</button>
                        </div>

                        <!-- FAQ 列表 -->
                        <div id="kbSection-faq" class="knowledge-section active">
                            <div id="faqList" class="faq-list"></div>
                        </div>

                        <!-- 文档管理 -->
                        <div id="kbSection-docs" class="knowledge-section">
                            <div class="settings-group">
                                <div class="settings-group-header">
                                    <div class="settings-group-title">文档同步</div>
                                    <div class="settings-group-description">支持从Markdown/网页链接导入，建立向量索引供AI检索</div>
                                </div>
                                <div class="settings-item">
                                    <div class="settings-item-info">
                                        <div class="settings-item-title">导入方式</div>
                                        <div class="settings-item-description">支持粘贴URL或直接粘贴Markdown内容</div>
                                    </div>
                                    <div class="settings-control">
                                        <button class="material-button outlined" onclick="importDoc('url')">从链接导入</button>
                                        <button class="material-button outlined" onclick="importDoc('md')">从Markdown导入</button>
                                    </div>
                                </div>
                            </div>

                            <div class="chart-container">
                                <div class="chart-header">
                                    <div class="chart-title">索引健康度</div>
                                    <div class="text-button" onclick="rebuildIndex()">重建索引</div>
                                </div>
                                <div class="chart-placeholder">图表占位：已索引文档 12 篇 · 索引块 1,248 · 失败 3</div>
                            </div>
                        </div>

                        <!-- 挖掘 -->
                        <div id="kbSection-mined" class="knowledge-section">
                            <div class="settings-group">
                                <div class="settings-group-header">
                                    <div class="settings-group-title">高频问题挖掘</div>
                                    <div class="settings-group-description">基于近7天对话自动沉淀潜在FAQ，支持一键转存</div>
                                </div>
                                <div class="settings-item">
                                    <div class="settings-item-info">
                                        <div class="settings-item-title">候选问题</div>
                                        <div class="settings-item-description">检测到 9 条候选，覆盖 咨询/售后/价格 等主题</div>
                                    </div>
                                    <div class="settings-control">
                                        <button class="material-button filled" onclick="oneClickCurate()">一键沉淀</button>
                                    </div>
                                </div>
                            </div>

                            <div id="minedList" class="faq-list"></div>
                        </div>
                    </div>
                </div>

                <!-- 数据分析视图 -->
                <div id="analyticsView" class="content-view">
                    <div class="analytics-container">
                        <h2 class="md-typescale-headline-medium">数据分析</h2>
                        <p class="md-typescale-body-medium" style="color: var(--md-sys-color-on-surface-variant); margin-top: 8px;">
                            查看消息统计、AI性能和用户洞察数据
                        </p>

                        <div class="analytics-overview" style="margin-top: 24px;">
                            <div class="metric-card">
                                <div class="metric-value" id="metric-msg">12,480</div>
                                <div class="metric-label">消息总量（7天）</div>
                                <div class="metric-change positive">较上周 +8.6%</div>
                            </div>
                            <div class="metric-card">
                                <div class="metric-value" id="metric-ai">68.3%</div>
                                <div class="metric-label">AI自助解决率</div>
                                <div class="metric-change positive">较上周 +2.1%</div>
                            </div>
                            <div class="metric-card">
                                <div class="metric-value" id="metric-sla">96.2%</div>
                                <div class="metric-label">响应SLA达成</div>
                                <div class="metric-change negative">较上周 -0.4%</div>
                            </div>
                            <div class="metric-card">
                                <div class="metric-value" id="metric-csat">4.78</div>
                                <div class="metric-label">用户满意度（满分5）</div>
                                <div class="metric-change positive">较上周 +0.05</div>
                            </div>
                        </div>

                        <div class="chart-container">
                            <div class="chart-header">
                                <div class="chart-title">消息量趋势</div>
                                <div>
                                    <select id="analyticsRange" onchange="updateAnalyticsRange()">
                                        <option value="7d">近7天</option>
                                        <option value="30d">近30天</option>
                                        <option value="90d">近90天</option>
                                    </select>
                                </div>
                            </div>
                            <div class="chart-placeholder">图表占位：消息量随时间变化折线图</div>
                        </div>

                        <div class="chart-container">
                            <div class="chart-header">
                                <div class="chart-title">渠道对比</div>
                                <div class="text-button" onclick="toggleChannelCompare()">切换对比维度</div>
                            </div>
                            <div class="chart-placeholder">图表占位：各渠道消息量/AI解决率的柱状对比</div>
                        </div>

                        <div class="chart-container">
                            <div class="chart-header">
                                <div class="chart-title">AI建议采纳率</div>
                                <div class="text-button" onclick="toggleAISuggestionMetric()">切换指标</div>
                            </div>
                            <div class="chart-placeholder">图表占位：采纳率/响应速度/准确率雷达图</div>
                        </div>
                    </div>
                </div>

                <!-- 系统设置视图 -->
                <div id="settingsView" class="content-view">
                    <div class="settings-container">
                        <h2 class="md-typescale-headline-medium">系统设置</h2>
                        <p class="md-typescale-body-medium" style="color: var(--md-sys-color-on-surface-variant); margin-top: 8px;">
                            配置账户信息、通知设置和安全选项
                        </p>

                        <div class="settings-nav" style="margin-top: 16px;">
                            <button class="settings-nav-item active" onclick="switchSettingsTab(event, 'account')">账户</button>
                            <button class="settings-nav-item" onclick="switchSettingsTab(event, 'notifications')">通知</button>
                            <button class="settings-nav-item" onclick="switchSettingsTab(event, 'security')">安全</button>
                        </div>

                        <!-- 账户设置 -->
                        <div id="settings-account" class="settings-section active">
                            <div class="settings-group">
                                <div class="settings-group-header">
                                    <div class="settings-group-title">基本信息</div>
                                    <div class="settings-group-description">用于在系统中展示你的身份信息</div>
                                </div>
                                <div class="settings-item">
                                    <div class="form-row" style="width: 100%;">
                                        <div class="form-group">
                                            <label>昵称</label>
                                            <input id="profileName" type="text" placeholder="请输入昵称" value="思思" />
                                        </div>
                                        <div class="form-group">
                                            <label>角色</label>
                                            <select id="profileRole">
                                                <option>知识IP主理人</option>
                                                <option>客服经理</option>
                                                <option>运营同学</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="settings-item">
                                    <div class="form-row" style="width: 100%;">
                                        <div class="form-group">
                                            <label>个性签名</label>
                                            <textarea id="profileBio" placeholder="一句话介绍你自己">用AI把时间还给内容创作</textarea>
                                        </div>
                                    </div>
                                </div>
                                <div class="settings-item" style="justify-content: flex-end;">
                                    <button class="material-button outlined" onclick="resetProfile()">重置</button>
                                    <button class="material-button filled" onclick="saveProfile()">保存</button>
                                </div>
                            </div>
                        </div>

                        <!-- 通知设置 -->
                        <div id="settings-notifications" class="settings-section">
                            <div class="settings-group">
                                <div class="settings-group-header">
                                    <div class="settings-group-title">通知渠道</div>
                                    <div class="settings-group-description">配置系统事件的通知方式</div>
                                </div>
                                <div class="settings-item">
                                    <div class="settings-item-info">
                                        <div class="settings-item-title">邮件推送</div>
                                        <div class="settings-item-description">当有重要事件时发送邮件</div>
                                    </div>
                                    <div class="settings-control">
                                        <label class="switch">
                                            <input id="emailNotify" type="checkbox" checked />
                                            <span class="slider"></span>
                                        </label>
                                    </div>
                                </div>
                                <div class="settings-item">
                                    <div class="settings-item-info">
                                        <div class="settings-item-title">短信提醒</div>
                                        <div class="settings-item-description">AI接管、SLA预警将发送短信</div>
                                    </div>
                                    <div class="settings-control">
                                        <label class="switch">
                                            <input id="smsNotify" type="checkbox" />
                                            <span class="slider"></span>
                                        </label>
                                    </div>
                                </div>
                                <div class="settings-item">
                                    <div class="settings-item-info">
                                        <div class="settings-item-title">应用内通知</div>
                                        <div class="settings-item-description">在右上角消息中心展示</div>
                                    </div>
                                    <div class="settings-control">
                                        <label class="switch">
                                            <input id="inappNotify" type="checkbox" checked />
                                            <span class="slider"></span>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="settings-group">
                                <div class="settings-group-header">
                                    <div class="settings-group-title">触发规则</div>
                                    <div class="settings-group-description">选择哪些事件会触发通知</div>
                                </div>
                                <div class="settings-item">
                                    <div class="settings-item-info">
                                        <div class="settings-item-title">关键词警报</div>
                                        <div class="settings-item-description">出现敏感词时触发通知</div>
                                    </div>
                                    <div class="settings-control">
                                        <label class="switch">
                                            <input id="keywordAlert" type="checkbox" />
                                            <span class="slider"></span>
                                        </label>
                                    </div>
                                </div>
                                <div class="settings-item">
                                    <div class="settings-item-info">
                                        <div class="settings-item-title">AI接管</div>
                                        <div class="settings-item-description">当AI自动接管工单时提醒</div>
                                    </div>
                                    <div class="settings-control">
                                        <label class="switch">
                                            <input id="aiTakeoverAlert" type="checkbox" checked />
                                            <span class="slider"></span>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="settings-group">
                                <div class="settings-item" style="justify-content: flex-end;">
                                    <button class="material-button outlined" onclick="resetNotifications()">重置</button>
                                    <button class="material-button filled" onclick="saveNotifications()">保存</button>
                                </div>
                            </div>
                        </div>

                        <!-- 安全设置 -->
                        <div id="settings-security" class="settings-section">
                            <div class="settings-group">
                                <div class="settings-group-header">
                                    <div class="settings-group-title">登录与验证</div>
                                    <div class="settings-group-description">提升账号安全性，保护数据</div>
                                </div>
                                <div class="settings-item">
                                    <div class="settings-item-info">
                                        <div class="settings-item-title">两步验证</div>
                                        <div class="settings-item-description">登录时需要短信验证码</div>
                                    </div>
                                    <div class="settings-control">
                                        <label class="switch">
                                            <input id="twoFactor" type="checkbox" checked />
                                            <span class="slider"></span>
                                        </label>
                                    </div>
                                </div>
                                <div class="settings-item">
                                    <div class="settings-item-info">
                                        <div class="settings-item-title">设备管理</div>
                                        <div class="settings-item-description">管理已登录设备，异常登录会告警</div>
                                    </div>
                                    <div class="settings-control">
                                        <button class="material-button outlined" onclick="manageDevices()">查看设备</button>
                                    </div>
                                </div>
                            </div>

                            <div class="settings-group">
                                <div class="settings-group-header">
                                    <div class="settings-group-title">数据与隐私</div>
                                    <div class="settings-group-description">导出数据或清理对话记录</div>
                                </div>
                                <div class="settings-item">
                                    <div class="settings-item-info">
                                        <div class="settings-item-title">导出数据</div>
                                        <div class="settings-item-description">导出知识库、对话与分析报表</div>
                                    </div>
                                    <div class="settings-control">
                                        <button class="material-button outlined" onclick="exportData()">导出</button>
                                    </div>
                                </div>
                                <div class="settings-item">
                                    <div class="settings-item-info">
                                        <div class="settings-item-title">清理历史</div>
                                        <div class="settings-item-description">清理超过6个月的历史数据</div>
                                    </div>
                                    <div class="settings-control">
                                        <button class="material-button outlined" onclick="cleanHistory()">清理</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局状态管理
        const AppState = {
            currentUser: null,
            currentView: 'workspace',
            currentConversation: null,
            aiMode: false,
            loginMode: 'sms', // 'sms' or 'password'
            smsCountdown: 0,
            
            // 知识库数据
            knowledge: {
                currentTab: 'faq',
                faqs: [
                    {
                        id: 1,
                        question: '如何修改账户密码？',
                        answer: '您可以在系统设置 > 安全 > 修改密码页面进行操作。',
                        source: 'faq',
                        status: 'active',
                        tags: ['账户', '安全'],
                        usage: 24,
                        updated: '2024-01-10'
                    },
                    {
                        id: 2,
                        question: '如何开通AI托管功能？',
                        answer: 'AI托管功能需要升级到专业版或企业版，请联系销售顾问。',
                        source: 'faq',
                        status: 'active',
                        tags: ['AI', '功能'],
                        usage: 18,
                        updated: '2024-01-12'
                    },
                    {
                        id: 3,
                        question: '消息回复速度慢怎么解决？',
                        answer: '可能是网络问题或服务器负载高，请尝试刷新页面或稍后重试。',
                        source: 'faq',
                        status: 'draft',
                        tags: ['技术', '故障'],
                        usage: 0,
                        updated: '2024-01-13'
                    }
                ],
                minedQuestions: [
                    {
                        id: 101,
                        question: '退款流程是什么？',
                        frequency: 15,
                        source: 'chat',
                        status: 'candidate',
                        confidence: 0.89
                    },
                    {
                        id: 102,
                        question: '支持哪些付款方式？',
                        frequency: 12,
                        source: 'chat',
                        status: 'candidate',
                        confidence: 0.92
                    }
                ]
            },
            
            // 数据分析状态
            analytics: {
                currentRange: '7d',
                comparisonMode: 'volume'
            },
            
            // 系统设置状态
            settings: {
                currentTab: 'account',
                profile: {
                    name: '思思',
                    role: '知识IP主理人',
                    bio: '用AI把时间还给内容创作'
                },
                notifications: {
                    email: true,
                    sms: false,
                    inapp: true,
                    keywordAlert: false,
                    aiTakeover: true
                },
                security: {
                    twoFactor: true
                }
            }
        };

        // 登录相关功能
        function switchLoginTab(mode) {
            AppState.loginMode = mode;

            // 更新标签样式
            document.querySelectorAll('.login-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.classList.add('active');

            // 显示/隐藏对应表单
            const smsForm = document.getElementById('smsForm');
            const passwordForm = document.getElementById('passwordForm');

            if (mode === 'sms') {
                smsForm.classList.remove('hidden');
                passwordForm.classList.add('hidden');
            } else {
                smsForm.classList.add('hidden');
                passwordForm.classList.remove('hidden');
            }

            // 清除错误信息
            clearErrors();
        }

        function validatePhone(phone) {
            const phoneRegex = /^1[3-9]\d{9}$/;
            return phoneRegex.test(phone);
        }

        function getSmsCode() {
            const phoneInput = document.getElementById('phoneInput');
            const phone = phoneInput.value.trim();

            // 清除之前的错误
            clearErrors();

            // 验证手机号
            if (!phone) {
                showError('phoneError', '请输入手机号');
                return;
            }

            if (!validatePhone(phone)) {
                showError('phoneError', '请输入正确的手机号格式');
                return;
            }

            // 模拟发送验证码
            const getSmsBtn = document.getElementById('getSmsBtn');
            getSmsBtn.disabled = true;
            AppState.smsCountdown = 60;

            const countdown = setInterval(() => {
                getSmsBtn.textContent = `${AppState.smsCountdown}秒后重试`;
                AppState.smsCountdown--;

                if (AppState.smsCountdown < 0) {
                    clearInterval(countdown);
                    getSmsBtn.disabled = false;
                    getSmsBtn.textContent = '获取验证码';
                }
            }, 1000);

            // 显示成功消息
            showSnackbar('验证码已发送，请查收短信');
        }

        function login() {
            // ===== 开发调试模式 - 简化登录流程 =====
            console.log('🚀 调试模式：跳过登录验证，直接进入主应用');

            const phoneInput = document.getElementById('phoneInput');
            const phone = phoneInput.value.trim() || '13800138000'; // 默认手机号

            // 清除之前的错误
            clearErrors();

            /* ===== 以下为原始验证逻辑，已注释用于调试 =====

            // 验证手机号
            if (!phone) {
                showError('phoneError', '请输入手机号');
                return;
            }

            if (!validatePhone(phone)) {
                showError('phoneError', '请输入正确的手机号格式');
                return;
            }

            if (AppState.loginMode === 'sms') {
                const smsCode = document.getElementById('smsCodeInput').value.trim();
                if (!smsCode) {
                    showError('smsError', '请输入验证码');
                    return;
                }
                if (smsCode.length !== 6) {
                    showError('smsError', '验证码应为6位数字');
                    return;
                }

                // 模拟验证码验证
                if (smsCode !== '123456') {
                    showError('smsError', '验证码错误，请重新输入');
                    return;
                }
            } else {
                const password = document.getElementById('passwordInput').value.trim();
                if (!password) {
                    showError('passwordError', '请输入密码');
                    return;
                }

                // 模拟密码验证
                if (password !== 'password123') {
                    showError('passwordError', '密码错误，请重新输入');
                    return;
                }
            }

            ===== 原始验证逻辑结束 ===== */

            // 登录成功 - 设置默认用户信息
            AppState.currentUser = {
                phone: phone,
                name: '思思',
                role: '知识IP主理人'
            };

            showSnackbar('🚀 调试模式：登录成功！');

            // 延迟跳转到主应用
            setTimeout(() => {
                switchPage('mainApp');
            }, 1000);
        }

        function showRegister() {
            showSnackbar('注册功能开发中，请使用演示账号登录');
        }

        function showForgotPassword() {
            showSnackbar('密码重置功能开发中');
        }

        // 主应用功能
        function switchView(viewName) {
            // 更新导航状态
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.classList.add('active');

            // 更新内容视图
            document.querySelectorAll('.content-view').forEach(view => {
                view.classList.remove('active');
            });
            document.getElementById(viewName + 'View').classList.add('active');

            // 更新页面标题
            const titles = {
                workspace: '工作台',
                channels: '渠道管理',
                knowledge: '知识库',
                analytics: '数据分析',
                settings: '系统设置'
            };
            document.getElementById('pageTitle').textContent = titles[viewName];

            AppState.currentView = viewName;
        }

        function selectConversation(element, name) {
            // 更新选中状态
            document.querySelectorAll('.conversation-item').forEach(item => {
                item.classList.remove('active');
            });
            element.classList.add('active');

            // 清除未读标记
            const unreadBadge = element.querySelector('.unread-badge');
            if (unreadBadge) {
                unreadBadge.remove();
            }

            AppState.currentConversation = name;

            // 更新聊天标题
            const chatTitle = document.querySelector('.chat-title h3');
            chatTitle.textContent = name;
        }

        function toggleAIMode() {
            AppState.aiMode = !AppState.aiMode;
            const aiStatus = document.getElementById('aiStatus');

            if (AppState.aiMode) {
                aiStatus.textContent = 'AI托管中';
                aiStatus.className = 'ai-status auto';
                showSnackbar('AI托管模式已开启');
            } else {
                aiStatus.textContent = '人工模式';
                aiStatus.className = 'ai-status manual';
                showSnackbar('已切换到人工模式');
            }
        }

        function sendMessage() {
            const messageInput = document.getElementById('messageInput');
            const message = messageInput.value.trim();

            if (!message) return;

            // 添加消息到聊天区域
            const chatMessages = document.getElementById('chatMessages');
            const messageElement = createMessageElement(message, true);
            chatMessages.appendChild(messageElement);

            // 清空输入框
            messageInput.value = '';

            // 滚动到底部
            chatMessages.scrollTop = chatMessages.scrollHeight;

            // 如果是AI托管模式，模拟AI回复
            if (AppState.aiMode) {
                setTimeout(() => {
                    simulateAIReply();
                }, 1000);
            }
        }

        function createMessageElement(content, isSent = false) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isSent ? 'sent' : ''}`;

            const avatar = document.createElement('div');
            avatar.className = 'message-avatar';
            avatar.textContent = isSent ? '思' : '张';

            const messageContent = document.createElement('div');
            messageContent.className = 'message-content';

            const messageText = document.createElement('div');
            messageText.textContent = content;

            const messageTime = document.createElement('div');
            messageTime.className = 'message-time';
            messageTime.textContent = new Date().toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            });

            messageContent.appendChild(messageText);
            messageContent.appendChild(messageTime);

            messageDiv.appendChild(avatar);
            messageDiv.appendChild(messageContent);

            return messageDiv;
        }

        function simulateAIReply() {
            const replies = [
                '感谢您的咨询！我来为您详细介绍一下我们的产品。',
                '根据您的需求，我推荐以下几个方案...',
                '这个问题很好，让我为您详细解答。'
            ];

            const randomReply = replies[Math.floor(Math.random() * replies.length)];
            const chatMessages = document.getElementById('chatMessages');
            const messageElement = createMessageElement(randomReply, false);
            chatMessages.appendChild(messageElement);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function useSuggestion(element) {
            const suggestion = element.textContent;
            const messageInput = document.getElementById('messageInput');
            messageInput.value = suggestion;
            messageInput.focus();
        }

        function handleEnterKey(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            }
        }

        // 渠道管理功能
        function addChannel() {
            showSnackbar('添加渠道功能开发中');
        }

        function editChannel(channelId) {
            showSnackbar(`编辑渠道 ${channelId} 功能开发中`);
        }

        function pauseChannel(channelId) {
            showSnackbar(`渠道 ${channelId} 已暂停`);
        }

        // 工具函数
        function switchPage(pageId) {
            console.log('切换到页面:', pageId);
            document.querySelectorAll('.page').forEach(page => {
                page.classList.remove('active');
            });
            const targetPage = document.getElementById(pageId);
            if (targetPage) {
                targetPage.classList.add('active');
                console.log('页面切换成功:', pageId);
            } else {
                console.error('找不到页面元素:', pageId);
            }
        }

        function showError(elementId, message) {
            const errorElement = document.getElementById(elementId);
            errorElement.textContent = message;
            errorElement.classList.remove('hidden');
        }

        function clearErrors() {
            document.querySelectorAll('.error-message').forEach(error => {
                error.classList.add('hidden');
                error.textContent = '';
            });
        }

        function showSnackbar(message) {
            // 创建简单的提示消息
            const snackbar = document.createElement('div');
            snackbar.style.cssText = `
                position: fixed;
                bottom: 24px;
                left: 50%;
                transform: translateX(-50%);
                background: var(--md-sys-color-on-surface);
                color: var(--md-sys-color-surface);
                padding: 12px 24px;
                border-radius: 8px;
                z-index: 10000;
                animation: fadeIn 0.3s ease-in-out;
            `;
            snackbar.textContent = message;

            document.body.appendChild(snackbar);

            setTimeout(() => {
                snackbar.remove();
            }, 3000);
        }

        function showNotifications() {
            showSnackbar('通知功能开发中');
        }

        function showUserMenu() {
            showSnackbar('用户菜单功能开发中');
        }

        function attachFile() {
            showSnackbar('文件上传功能开发中');
        }

        // ====== 知识库：渲染与交互 ======
        function renderFAQList() {
            const list = document.getElementById('faqList');
            if (!list) return;
            const { faqs } = AppState.knowledge;

            const keyword = (document.getElementById('kbSearchInput')?.value || '').trim().toLowerCase();
            const src = document.getElementById('kbSourceFilter')?.value || 'all';
            const status = document.getElementById('kbStatusFilter')?.value || 'all';

            const filtered = faqs.filter(item => {
                const hitKeyword = !keyword || item.question.toLowerCase().includes(keyword) || item.tags.join(',').toLowerCase().includes(keyword);
                const hitSource = src === 'all' || item.source === src;
                const hitStatus = status === 'all' || item.status === status;
                return hitKeyword && hitSource && hitStatus;
            });

            list.innerHTML = filtered.map(item => `
                <div class="faq-item">
                    <div class="faq-header">
                        <div class="faq-question">${item.question}</div>
                        <div style="display:flex; gap:8px; align-items:center;">
                            <span class="faq-meta">使用 ${item.usage}</span>
                            <span class="status-badge ${item.status}">${item.status === 'active' ? '启用' : '草稿'}</span>
                            <button class="icon-button" title="编辑" onclick="editFAQ(${item.id})">
                                <span class="material-icons">edit</span>
                            </button>
                            <button class="icon-button" title="更多" onclick="moreFAQ(${item.id})">
                                <span class="material-icons">more_vert</span>
                            </button>
                        </div>
                    </div>
                    <div class="faq-answer">${item.answer}</div>
                    <div class="faq-meta">来源：${item.source.toUpperCase()} · 标签：${item.tags.join('，')} · 更新于 ${item.updated}</div>
                </div>
            `).join('');
        }

        function renderMinedList() {
            const list = document.getElementById('minedList');
            if (!list) return;
            const items = AppState.knowledge.minedQuestions;
            list.innerHTML = items.map(q => `
                <div class="faq-item">
                    <div class="faq-header">
                        <div class="faq-question">${q.question}</div>
                        <div style="display:flex; gap:8px; align-items:center;">
                            <span class="faq-meta">出现 ${q.frequency} 次</span>
                            <span class="status-badge">可信度 ${(q.confidence*100).toFixed(0)}%</span>
                            <button class="material-button outlined" onclick="convertToFAQ(${q.id})">转存为FAQ</button>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        function filterKnowledge() {
            renderFAQList();
        }

        function switchKnowledgeTab(evt, tab) {
            AppState.knowledge.currentTab = tab;
            document.querySelectorAll('.knowledge-tab').forEach(b => b.classList.remove('active'));
            evt.currentTarget.classList.add('active');

            document.querySelectorAll('.knowledge-section').forEach(sec => sec.classList.remove('active'));
            document.getElementById(`kbSection-${tab}`).classList.add('active');

            if (tab === 'faq') renderFAQList();
            if (tab === 'mined') renderMinedList();
        }

        function openKnowledgeEditor() {
            showSnackbar('打开知识编辑器（原型占位）');
        }
        function editFAQ(id) { showSnackbar(`编辑FAQ #${id}（原型占位）`); }
        function moreFAQ(id) { showSnackbar(`更多操作 #${id}（原型占位）`); }
        function importDoc(type) { showSnackbar(`从${type==='url'?'链接':'Markdown'}导入（原型占位）`); }
        function rebuildIndex() { showSnackbar('开始重建索引（原型占位）'); }
        function oneClickCurate() { showSnackbar('已将候选问题转存为FAQ（原型占位）'); }
        function convertToFAQ(id) { showSnackbar(`候选 ${id} 已转存（原型占位）`); }

        // ====== 数据分析：交互 ======
        function updateAnalyticsRange() {
            const select = document.getElementById('analyticsRange');
            if (!select) return;
            AppState.analytics.currentRange = select.value;
            showSnackbar(`已切换为 ${select.value} 时间范围`);
        }
        function toggleChannelCompare() {
            AppState.analytics.comparisonMode = AppState.analytics.comparisonMode === 'volume' ? 'resolution' : 'volume';
            showSnackbar(`渠道对比维度：${AppState.analytics.comparisonMode==='volume'?'消息量':'AI解决率'}`);
        }
        function toggleAISuggestionMetric() {
            showSnackbar('切换AI建议指标（原型占位）');
        }

        // ====== 设置：交互 ======
        function switchSettingsTab(evt, tab) {
            AppState.settings.currentTab = tab;
            document.querySelectorAll('.settings-nav-item').forEach(b => b.classList.remove('active'));
            evt.currentTarget.classList.add('active');
            document.querySelectorAll('.settings-section').forEach(sec => sec.classList.remove('active'));
            document.getElementById(`settings-${tab}`).classList.add('active');
        }

        function resetProfile() {
            document.getElementById('profileName').value = AppState.settings.profile.name;
            document.getElementById('profileRole').value = AppState.settings.profile.role;
            document.getElementById('profileBio').value = AppState.settings.profile.bio;
            showSnackbar('已恢复默认信息');
        }
        function saveProfile() {
            AppState.settings.profile.name = document.getElementById('profileName').value.trim();
            AppState.settings.profile.role = document.getElementById('profileRole').value;
            AppState.settings.profile.bio = document.getElementById('profileBio').value.trim();
            showSnackbar('资料已保存');
        }

        function resetNotifications() {
            document.getElementById('emailNotify').checked = AppState.settings.notifications.email;
            document.getElementById('smsNotify').checked = AppState.settings.notifications.sms;
            document.getElementById('inappNotify').checked = AppState.settings.notifications.inapp;
            document.getElementById('keywordAlert').checked = AppState.settings.notifications.keywordAlert;
            document.getElementById('aiTakeoverAlert').checked = AppState.settings.notifications.aiTakeover;
            showSnackbar('已恢复默认通知配置');
        }
        function saveNotifications() {
            AppState.settings.notifications.email = document.getElementById('emailNotify').checked;
            AppState.settings.notifications.sms = document.getElementById('smsNotify').checked;
            AppState.settings.notifications.inapp = document.getElementById('inappNotify').checked;
            AppState.settings.notifications.keywordAlert = document.getElementById('keywordAlert').checked;
            AppState.settings.notifications.aiTakeover = document.getElementById('aiTakeoverAlert').checked;
            showSnackbar('通知设置已保存');
        }

        function manageDevices() { showSnackbar('设备管理（原型占位）'); }
        function exportData() { showSnackbar('开始导出（原型占位）'); }
        function cleanHistory() { showSnackbar('清理完成（原型占位）'); }

        // 初始化应用
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 柴管家产品原型已加载 - 调试模式');
            console.log('🔧 调试模式说明：');
            console.log('   - 登录验证已简化，点击登录按钮即可直接进入');
            console.log('   - 无需填写手机号和验证码');
            console.log('   - 这是开发调试版本，正式版本需要恢复完整验证');

            // 设置调试模式提示
            setTimeout(() => {
                if (document.getElementById('loginPage').classList.contains('active')) {
                    showSnackbar('🚀 调试模式：点击登录按钮即可直接进入（无需填写信息）');
                }
            }, 2000);

            // 在页面标题添加调试标识
            document.title = '柴管家 - 多平台聚合智能客服系统 [调试模式]';

            // 初始化渲染：知识库/挖掘列表与设置默认值
            try {
                renderFAQList();
                renderMinedList();
                resetProfile();
                resetNotifications();
            } catch (e) {
                console.warn('初始化渲染时机：', e);
            }
        });

        // 响应式处理
        function handleResize() {
            const isMobile = window.innerWidth <= 768;

            if (isMobile) {
                // 移动端适配逻辑
                document.body.classList.add('mobile');
            } else {
                document.body.classList.remove('mobile');
            }
        }

        window.addEventListener('resize', handleResize);
        handleResize(); // 初始化时执行一次
    </script>
</body>
</html>
